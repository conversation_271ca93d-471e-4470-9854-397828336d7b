#!/usr/bin/env python3
"""
Compare Latest System Health Report with Historical Analysis
This script compares the latest system health report with our comprehensive historical analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os

class LatestReportComparison:
    def __init__(self):
        self.reports_dir = "reports"
        self.visualizations_dir = "visualizations"
        self.comparison_dir = "latest_comparison"
        
        # Create comparison directory
        os.makedirs(self.comparison_dir, exist_ok=True)
        
        # Load data
        self.load_data()
    
    def load_data(self):
        """Load latest report and historical analysis data"""
        print("Loading latest system health report and historical data...")
        
        try:
            # Load latest system health report
            self.latest_report = pd.read_csv("data/Latest_System_Health_Report/LatestSystemHealthReport.csv")
            print(f"Latest report loaded: {len(self.latest_report)} systems")
            
            # Load historical deduplicated data
            self.historical_data = pd.read_csv("reports/deduplicated_systems.csv")
            print(f"Historical data loaded: {len(self.historical_data)} systems")
            
            return True
            
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
    
    def analyze_latest_report(self):
        """Analyze the latest system health report"""
        print("\nAnalyzing latest system health report...")
        
        # Basic statistics
        total_systems = len(self.latest_report)
        
        # Health status distribution
        health_distribution = self.latest_report['Health'].value_counts()
        
        # Calculate percentages
        health_percentages = (health_distribution / total_systems * 100).round(1)
        
        # Operating system analysis
        os_distribution = self.latest_report['Operating System'].value_counts()
        
        # Patch analysis
        patch_columns = ['Missing OS Patches', 'Missing TP Patches', 'Missing Driver Patches', 'Missing BIOS Patches']
        
        # Convert patch columns to numeric
        for col in patch_columns:
            self.latest_report[col] = pd.to_numeric(self.latest_report[col], errors='coerce').fillna(0)
        
        # Calculate total missing patches per system
        self.latest_report['Total_Missing_Patches'] = self.latest_report[patch_columns].sum(axis=1)
        
        # Analyze patch statistics by health status
        patch_stats = {}
        for health_status in health_distribution.index:
            subset = self.latest_report[self.latest_report['Health'] == health_status]
            patch_stats[health_status] = {
                'avg_total_patches': subset['Total_Missing_Patches'].mean(),
                'avg_os_patches': subset['Missing OS Patches'].mean(),
                'avg_tp_patches': subset['Missing TP Patches'].mean(),
                'max_total_patches': subset['Total_Missing_Patches'].max(),
                'systems_count': len(subset)
            }
        
        # Last patched analysis
        self.latest_report['Last Patched Time'] = pd.to_datetime(self.latest_report['Last Patched Time'], errors='coerce')
        current_date = pd.Timestamp.now()
        self.latest_report['days_since_last_patch'] = (current_date - self.latest_report['Last Patched Time']).dt.days
        
        latest_analysis = {
            'total_systems': total_systems,
            'health_distribution': health_distribution.to_dict(),
            'health_percentages': health_percentages.to_dict(),
            'os_distribution': os_distribution.to_dict(),
            'patch_stats': patch_stats,
            'avg_days_since_patch': self.latest_report['days_since_last_patch'].mean(),
            'systems_never_patched': self.latest_report['Last Patched Time'].isna().sum()
        }
        
        return latest_analysis
    
    def compare_with_historical(self, latest_analysis):
        """Compare latest report with historical analysis"""
        print("\nComparing latest report with historical analysis...")
        
        # Historical health distribution
        historical_health = self.historical_data['category'].value_counts()
        historical_total = len(self.historical_data)
        historical_percentages = (historical_health / historical_total * 100).round(1)
        
        # Create comparison
        comparison = {
            'system_counts': {
                'latest_total': latest_analysis['total_systems'],
                'historical_total': historical_total,
                'difference': latest_analysis['total_systems'] - historical_total
            },
            'health_comparison': {}
        }
        
        # Map historical categories to latest health statuses
        category_mapping = {
            'Highly Vulnerable': 'Highly Vulnerable',
            'Vulnerable': 'Vulnerable', 
            'Healthy': 'Healthy'
        }
        
        for hist_cat, latest_cat in category_mapping.items():
            hist_count = historical_health.get(hist_cat, 0)
            hist_pct = historical_percentages.get(hist_cat, 0)
            latest_count = latest_analysis['health_distribution'].get(latest_cat, 0)
            latest_pct = latest_analysis['health_percentages'].get(latest_cat, 0)
            
            comparison['health_comparison'][latest_cat] = {
                'historical_count': hist_count,
                'historical_percentage': hist_pct,
                'latest_count': latest_count,
                'latest_percentage': latest_pct,
                'count_change': latest_count - hist_count,
                'percentage_change': latest_pct - hist_pct
            }
        
        return comparison
    
    def identify_system_changes(self):
        """Identify specific systems that changed status"""
        print("\nIdentifying system status changes...")
        
        # Find common systems between datasets
        latest_systems = set(self.latest_report['Computer Name'].str.upper())
        historical_systems = set(self.historical_data['Computer Name'].str.upper())
        
        common_systems = latest_systems.intersection(historical_systems)
        
        print(f"Common systems found: {len(common_systems)}")
        print(f"New systems in latest: {len(latest_systems - historical_systems)}")
        print(f"Missing systems from latest: {len(historical_systems - latest_systems)}")
        
        # Analyze status changes for common systems
        status_changes = []
        
        for system in common_systems:
            # Get latest status
            latest_row = self.latest_report[self.latest_report['Computer Name'].str.upper() == system]
            if not latest_row.empty:
                latest_status = latest_row.iloc[0]['Health']
                
                # Get historical status
                hist_row = self.historical_data[self.historical_data['Computer Name'].str.upper() == system]
                if not hist_row.empty:
                    hist_status = hist_row.iloc[0]['category']
                    
                    # Check if status changed
                    if latest_status != hist_status:
                        status_changes.append({
                            'Computer_Name': system,
                            'Historical_Status': hist_status,
                            'Latest_Status': latest_status,
                            'Change_Type': self.categorize_change(hist_status, latest_status)
                        })
        
        return status_changes, len(common_systems), len(latest_systems - historical_systems), len(historical_systems - latest_systems)
    
    def categorize_change(self, old_status, new_status):
        """Categorize the type of status change"""
        if old_status == 'Highly Vulnerable' and new_status == 'Healthy':
            return 'Major Improvement'
        elif old_status == 'Highly Vulnerable' and new_status == 'Vulnerable':
            return 'Improvement'
        elif old_status == 'Vulnerable' and new_status == 'Healthy':
            return 'Improvement'
        elif old_status == 'Healthy' and new_status == 'Vulnerable':
            return 'Degradation'
        elif old_status == 'Healthy' and new_status == 'Highly Vulnerable':
            return 'Major Degradation'
        elif old_status == 'Vulnerable' and new_status == 'Highly Vulnerable':
            return 'Degradation'
        else:
            return 'No Change'
    
    def create_comparison_visualizations(self, latest_analysis, comparison, status_changes):
        """Create comparison visualizations"""
        print("\nCreating comparison visualizations...")
        
        # Set up plotting
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 1. Side-by-side health distribution comparison
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))
        
        # Historical distribution
        hist_data = comparison['health_comparison']
        categories = list(hist_data.keys())
        hist_counts = [hist_data[cat]['historical_count'] for cat in categories]
        latest_counts = [hist_data[cat]['latest_count'] for cat in categories]
        
        colors = ['#ff4444', '#ffaa44', '#44ff44']  # Red, Orange, Green
        
        ax1.pie(hist_counts, labels=categories, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title(f'Historical Analysis\n({comparison["system_counts"]["historical_total"]} systems)', fontweight='bold')
        
        ax2.pie(latest_counts, labels=categories, colors=colors, autopct='%1.1f%%', startangle=90)
        ax2.set_title(f'Latest Report\n({comparison["system_counts"]["latest_total"]} systems)', fontweight='bold')
        
        # Change comparison bar chart
        changes = [hist_data[cat]['count_change'] for cat in categories]
        bar_colors = ['red' if x > 0 else 'green' if x < 0 else 'gray' for x in changes]
        
        bars = ax3.bar(categories, changes, color=bar_colors, alpha=0.7)
        ax3.set_title('System Count Changes\n(Latest - Historical)', fontweight='bold')
        ax3.set_ylabel('Change in System Count')
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax3.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, change in zip(bars, changes):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + (1 if height >= 0 else -3),
                    f'{change:+d}', ha='center', va='bottom' if height >= 0 else 'top', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.comparison_dir, 'health_distribution_comparison.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. Status changes analysis
        if status_changes:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # Status change types
            change_types = [change['Change_Type'] for change in status_changes]
            change_counts = pd.Series(change_types).value_counts()
            
            colors_map = {
                'Major Improvement': 'darkgreen',
                'Improvement': 'lightgreen', 
                'No Change': 'gray',
                'Degradation': 'orange',
                'Major Degradation': 'red'
            }
            
            colors = [colors_map.get(ct, 'gray') for ct in change_counts.index]
            
            ax1.bar(change_counts.index, change_counts.values, color=colors, alpha=0.8)
            ax1.set_title('System Status Changes', fontweight='bold')
            ax1.set_ylabel('Number of Systems')
            ax1.tick_params(axis='x', rotation=45)
            ax1.grid(True, alpha=0.3)
            
            # Patch analysis for latest report
            health_order = ['Healthy', 'Vulnerable', 'Highly Vulnerable']
            avg_patches = [latest_analysis['patch_stats'][status]['avg_total_patches'] 
                          for status in health_order if status in latest_analysis['patch_stats']]
            
            ax2.bar(health_order[:len(avg_patches)], avg_patches, color=colors[:len(avg_patches)], alpha=0.8)
            ax2.set_title('Average Missing Patches by Health Status\n(Latest Report)', fontweight='bold')
            ax2.set_ylabel('Average Missing Patches')
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(os.path.join(self.comparison_dir, 'status_changes_analysis.png'), 
                       dpi=300, bbox_inches='tight')
            plt.close()
        
        print("Comparison visualizations created successfully!")
    
    def generate_comparison_report(self, latest_analysis, comparison, status_changes, common_systems, new_systems, missing_systems):
        """Generate comprehensive comparison report"""
        print("\nGenerating comparison report...")
        
        report_path = os.path.join(self.comparison_dir, "latest_vs_historical_comparison.md")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# Latest System Health Report vs Historical Analysis Comparison\n\n")
            f.write(f"**Generated on:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## Executive Summary\n\n")
            f.write(f"**Latest Report Date:** Current (most recent scan)\n")
            f.write(f"**Historical Analysis Period:** April 11 - June 10, 2025 (60 days)\n\n")
            
            # System count comparison
            f.write("## System Count Comparison\n\n")
            f.write(f"- **Historical Analysis:** {comparison['system_counts']['historical_total']:,} unique systems\n")
            f.write(f"- **Latest Report:** {comparison['system_counts']['latest_total']:,} systems\n")
            f.write(f"- **Difference:** {comparison['system_counts']['difference']:+,} systems\n\n")
            
            f.write(f"### System Tracking\n")
            f.write(f"- **Common Systems:** {common_systems} (tracked in both datasets)\n")
            f.write(f"- **New Systems:** {new_systems} (appear only in latest report)\n")
            f.write(f"- **Missing Systems:** {missing_systems} (in historical but not in latest)\n\n")
            
            # Health status comparison
            f.write("## Health Status Comparison\n\n")
            f.write("| Status | Historical Count | Historical % | Latest Count | Latest % | Change | % Change |\n")
            f.write("|--------|------------------|--------------|--------------|----------|--------|----------|\n")
            
            for status, data in comparison['health_comparison'].items():
                f.write(f"| **{status}** | {data['historical_count']:,} | {data['historical_percentage']:.1f}% | ")
                f.write(f"{data['latest_count']:,} | {data['latest_percentage']:.1f}% | ")
                f.write(f"{data['count_change']:+,} | {data['percentage_change']:+.1f}% |\n")
            
            f.write("\n")
            
            # Key findings
            f.write("## Key Findings\n\n")
            
            # Determine overall trend
            hv_change = comparison['health_comparison']['Highly Vulnerable']['percentage_change']
            if hv_change > 5:
                trend = "⚠️ **DETERIORATING** - Significant increase in highly vulnerable systems"
            elif hv_change < -5:
                trend = "✅ **IMPROVING** - Significant decrease in highly vulnerable systems"
            else:
                trend = "➡️ **STABLE** - Minimal change in vulnerability distribution"
            
            f.write(f"### Overall Security Trend: {trend}\n\n")
            
            # Specific insights
            f.write("### Specific Insights\n\n")
            
            hv_latest = latest_analysis['health_percentages'].get('Highly Vulnerable', 0)
            if hv_latest > 40:
                f.write("- 🚨 **CRITICAL:** Over 40% of systems are highly vulnerable\n")
            elif hv_latest > 25:
                f.write("- ⚠️ **HIGH RISK:** Over 25% of systems are highly vulnerable\n")
            else:
                f.write("- ✅ **MANAGEABLE:** Less than 25% of systems are highly vulnerable\n")
            
            # Patch analysis
            if 'patch_stats' in latest_analysis:
                hv_patches = latest_analysis['patch_stats'].get('Highly Vulnerable', {}).get('avg_total_patches', 0)
                f.write(f"- **Average missing patches per highly vulnerable system:** {hv_patches:.1f}\n")
                
                never_patched = latest_analysis.get('systems_never_patched', 0)
                if never_patched > 0:
                    f.write(f"- ⚠️ **{never_patched} systems have never been patched**\n")
            
            f.write("\n")
            
            # Status changes
            if status_changes:
                f.write("## System Status Changes\n\n")
                f.write(f"**{len(status_changes)} systems changed status** between historical analysis and latest report:\n\n")
                
                # Group changes by type
                change_summary = {}
                for change in status_changes:
                    change_type = change['Change_Type']
                    if change_type not in change_summary:
                        change_summary[change_type] = []
                    change_summary[change_type].append(change['Computer_Name'])
                
                for change_type, systems in change_summary.items():
                    f.write(f"### {change_type}: {len(systems)} systems\n")
                    for system in systems[:10]:  # Show first 10
                        f.write(f"- {system}\n")
                    if len(systems) > 10:
                        f.write(f"- ... and {len(systems) - 10} more systems\n")
                    f.write("\n")
            
            # Recommendations
            f.write("## Recommendations\n\n")
            
            if hv_change > 0:
                f.write("### Immediate Actions (Deteriorating Trend)\n")
                f.write("1. **Emergency Response:** Investigate why highly vulnerable systems increased\n")
                f.write("2. **Patch Management:** Review and accelerate patching processes\n")
                f.write("3. **System Monitoring:** Increase scan frequency for early detection\n\n")
            
            f.write("### Ongoing Actions\n")
            f.write("1. **Focus on Persistent Issues:** Address systems that remain highly vulnerable\n")
            f.write("2. **Preventive Measures:** Implement automated patching where possible\n")
            f.write("3. **Regular Monitoring:** Continue monthly comparisons to track trends\n\n")
            
            f.write("## Supporting Files\n\n")
            f.write("- **Latest Report:** `data/Latest_System_Health_Report/LatestSystemHealthReport.csv`\n")
            f.write("- **Historical Analysis:** `reports/deduplicated_systems.csv`\n")
            f.write("- **Comparison Charts:** `latest_comparison/` folder\n\n")
            
            f.write("---\n")
            f.write("*This comparison provides insights into security posture changes over time.*\n")
        
        print(f"Comparison report saved to: {report_path}")
        return report_path
    
    def run_complete_comparison(self):
        """Run the complete comparison analysis"""
        print("Starting Latest Report vs Historical Analysis Comparison")
        print("=" * 70)
        
        # Analyze latest report
        latest_analysis = self.analyze_latest_report()
        
        # Compare with historical
        comparison = self.compare_with_historical(latest_analysis)
        
        # Identify system changes
        status_changes, common_systems, new_systems, missing_systems = self.identify_system_changes()
        
        # Create visualizations
        self.create_comparison_visualizations(latest_analysis, comparison, status_changes)
        
        # Generate report
        report_path = self.generate_comparison_report(latest_analysis, comparison, status_changes, 
                                                    common_systems, new_systems, missing_systems)
        
        # Print summary
        print("\n" + "=" * 70)
        print("COMPARISON ANALYSIS COMPLETE!")
        print("=" * 70)
        
        print(f"\nLatest Report Summary:")
        print(f"  - Total Systems: {latest_analysis['total_systems']:,}")
        for status, count in latest_analysis['health_distribution'].items():
            pct = latest_analysis['health_percentages'][status]
            print(f"  - {status}: {count:,} ({pct:.1f}%)")
        
        print(f"\nComparison with Historical:")
        for status, data in comparison['health_comparison'].items():
            change = data['count_change']
            pct_change = data['percentage_change']
            print(f"  - {status}: {change:+,} systems ({pct_change:+.1f}%)")
        
        if status_changes:
            print(f"\nSystem Status Changes: {len(status_changes)} systems changed status")
        
        print(f"\nDetailed report: {report_path}")
        
        return latest_analysis, comparison, status_changes

if __name__ == "__main__":
    # Run comparison analysis
    analyzer = LatestReportComparison()
    analyzer.run_complete_comparison()
