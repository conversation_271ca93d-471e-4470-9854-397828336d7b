# Latest System Health Report vs Historical Analysis Comparison

**Generated on:** 2025-06-10 13:59:18

## Executive Summary

**Latest Report Date:** Current (most recent scan)
**Historical Analysis Period:** April 11 - June 10, 2025 (60 days)

## System Count Comparison

- **Historical Analysis:** 801 unique systems
- **Latest Report:** 775 systems
- **Difference:** -26 systems

### System Tracking
- **Common Systems:** 759 (tracked in both datasets)
- **New Systems:** 16 (appear only in latest report)
- **Missing Systems:** 35 (in historical but not in latest)

## Health Status Comparison

| Status | Historical Count | Historical % | Latest Count | Latest % | Change | % Change |
|--------|------------------|--------------|--------------|----------|--------|----------|
| **Highly Vulnerable** | 397 | 49.6% | 365 | 47.1% | -32 | -2.5% |
| **Vulnerable** | 124 | 15.5% | 119 | 15.4% | -5 | -0.1% |
| **Healthy** | 280 | 35.0% | 275 | 35.5% | -5 | +0.5% |

## Key Findings

### Overall Security Trend: ➡️ **STABLE** - Minimal change in vulnerability distribution

### Specific Insights

- 🚨 **CRITICAL:** Over 40% of systems are highly vulnerable
- **Average missing patches per highly vulnerable system:** 11.6
- ⚠️ **83 systems have never been patched**

## System Status Changes

**4 systems changed status** between historical analysis and latest report:

### Degradation: 2 systems
- CAP-LT-1059
- TORCH-LT-8504

### Improvement: 1 systems
- TORCH-LT-7727

### Major Degradation: 1 systems
- TORCH-LT-8502

## Recommendations

### Ongoing Actions
1. **Focus on Persistent Issues:** Address systems that remain highly vulnerable
2. **Preventive Measures:** Implement automated patching where possible
3. **Regular Monitoring:** Continue monthly comparisons to track trends

## Supporting Files

- **Latest Report:** `data/Latest_System_Health_Report/LatestSystemHealthReport.csv`
- **Historical Analysis:** `reports/deduplicated_systems.csv`
- **Comparison Charts:** `latest_comparison/` folder

---
*This comparison provides insights into security posture changes over time.*
