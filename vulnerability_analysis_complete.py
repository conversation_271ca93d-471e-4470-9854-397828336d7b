#!/usr/bin/env python3
"""
Complete Vulnerability Analysis Tool
This single script performs all essential vulnerability analysis tasks:
1. Data organization and consolidation
2. Duplicate detection and correction
3. Comprehensive analysis and reporting
4. Visualization creation
5. Executive summary generation
"""

import os
import shutil
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')

class ComprehensiveVulnerabilityAnalyzer:
    def __init__(self, data_dir="data"):
        self.data_dir = data_dir
        self.organized_dir = "organized_data"
        self.reports_dir = "reports"
        self.visualizations_dir = "visualizations"
        self.combined_data_dir = "combined_data"
        
        # Define folder mappings for organization
        self.folder_mappings = {
            "Healthy Systems": "Healthy_Systems",
            "Highly Vulnerable Systems": "Highly_Vulnerable_Systems", 
            "Vulnerable Systems": "Vulnerable_Systems",
            "Missing Patches": "Missing_Patches",
            "System Health Report": "System_Health_Reports",
            "Patch Compliance Report": "Patch_Compliance_Reports",
            "Detailed Patch Summary": "Detailed_Patch_Summary",
            "Systems with APD tasks": "Systems_with_APD_tasks",
            "Systems requiring reboot": "Systems_requiring_reboot",
            "Installed Patches": "Installed_Patches",
            "Latest Patches": "Latest_Patches",
            "Scan Report": "Scan_Reports",
            "Tenable": "Tenable_Data"
        }
        
        self.create_directories()
    
    def create_directories(self):
        """Create organized directory structure"""
        directories = [
            self.organized_dir, self.reports_dir, self.visualizations_dir, self.combined_data_dir
        ]
        
        # Add subdirectories for organized data
        for folder_name in self.folder_mappings.values():
            directories.append(os.path.join(self.organized_dir, folder_name))
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def organize_files(self):
        """Organize CSV files into appropriate folders based on their names"""
        if not os.path.exists(self.data_dir):
            print(f"Data directory {self.data_dir} not found!")
            return {}
        
        files_moved = 0
        files_processed = {}
        
        for filename in os.listdir(self.data_dir):
            if filename.endswith('.csv'):
                target_folder = self.categorize_file(filename)
                
                if target_folder:
                    source_path = os.path.join(self.data_dir, filename)
                    dest_folder = os.path.join(self.organized_dir, target_folder)
                    dest_path = os.path.join(dest_folder, filename)
                    
                    shutil.copy2(source_path, dest_path)
                    files_moved += 1
                    
                    if target_folder not in files_processed:
                        files_processed[target_folder] = []
                    files_processed[target_folder].append(filename)
        
        print(f"File Organization Complete! Total files moved: {files_moved}")
        return files_processed
    
    def categorize_file(self, filename):
        """Determine which category a file belongs to based on its name"""
        filename_lower = filename.lower()
        
        if 'tenable' in filename_lower:
            return self.folder_mappings["Tenable"]
        
        for key, folder in self.folder_mappings.items():
            if key.lower().replace(" ", "_") in filename_lower.replace(" ", "_"):
                return folder
            key_words = key.lower().split()
            if all(word in filename_lower for word in key_words):
                return folder
        
        return None
    
    def load_and_combine_data(self, category):
        """Load and combine all CSV files from a specific category"""
        category_path = os.path.join(self.organized_dir, category)
        
        if not os.path.exists(category_path):
            return pd.DataFrame()
        
        csv_files = [f for f in os.listdir(category_path) if f.endswith('.csv')]
        if not csv_files:
            return pd.DataFrame()
        
        combined_data = []
        
        for file in csv_files:
            file_path = os.path.join(category_path, file)
            try:
                date_str = file.split('_')[0] if '_' in file else 'unknown'
                df = pd.read_csv(file_path)
                df['source_file'] = file
                df['scan_date'] = date_str
                combined_data.append(df)
            except Exception as e:
                print(f"Error reading {file}: {str(e)}")
                continue
        
        if combined_data:
            result = pd.concat(combined_data, ignore_index=True)
            return result
        
        return pd.DataFrame()
    
    def analyze_duplicates_and_correct(self):
        """Analyze duplicates and create corrected dataset"""
        print("\nAnalyzing duplicates and creating corrected dataset...")
        
        # Load datasets
        highly_vulnerable = self.load_and_combine_data("Highly_Vulnerable_Systems")
        vulnerable = self.load_and_combine_data("Vulnerable_Systems")
        healthy = self.load_and_combine_data("Healthy_Systems")
        
        # Save combined datasets
        datasets = {
            'highly_vulnerable': highly_vulnerable,
            'vulnerable': vulnerable,
            'healthy': healthy
        }
        
        for name, df in datasets.items():
            if not df.empty:
                output_path = os.path.join(self.combined_data_dir, f"{name}_combined.csv")
                df.to_csv(output_path, index=False)
        
        # Find computer name column
        computer_name_col = None
        for col in ['Computer Name', 'Computer', 'Hostname', 'System Name']:
            if col in highly_vulnerable.columns:
                computer_name_col = col
                break
        
        if not computer_name_col:
            print("No computer name column found!")
            return {}, pd.DataFrame()
        
        # Analyze duplicates
        all_data = []
        for category, df in [('Highly Vulnerable', highly_vulnerable), ('Vulnerable', vulnerable), ('Healthy', healthy)]:
            df_copy = df.copy()
            df_copy['category'] = category
            all_data.append(df_copy)
        
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # Create deduplicated dataset (most recent record per computer)
        if 'scan_date' in combined_data.columns:
            combined_data['scan_date_parsed'] = pd.to_datetime(combined_data['scan_date'], format='%m-%d-%Y', errors='coerce')
            combined_data = combined_data.sort_values([computer_name_col, 'scan_date_parsed'], ascending=[True, False])
            latest_records = combined_data.groupby(computer_name_col).first().reset_index()
        else:
            latest_records = combined_data.groupby(computer_name_col).first().reset_index()
        
        # Calculate true counts
        true_counts = latest_records['category'].value_counts().to_dict()
        
        # Save deduplicated dataset
        dedup_path = os.path.join(self.reports_dir, "deduplicated_systems.csv")
        latest_records.to_csv(dedup_path, index=False)
        
        # Calculate duplicate statistics
        original_counts = {
            'Highly Vulnerable': len(highly_vulnerable),
            'Vulnerable': len(vulnerable),
            'Healthy': len(healthy)
        }
        
        duplicate_stats = {
            'original_total': sum(original_counts.values()),
            'unique_systems': len(latest_records),
            'duplicate_records': sum(original_counts.values()) - len(latest_records),
            'original_counts': original_counts,
            'corrected_counts': true_counts
        }
        
        print(f"Duplicate Analysis Complete:")
        print(f"  - Original records: {duplicate_stats['original_total']:,}")
        print(f"  - Unique systems: {duplicate_stats['unique_systems']:,}")
        print(f"  - Duplicates removed: {duplicate_stats['duplicate_records']:,}")
        
        return duplicate_stats, latest_records
    
    def create_visualizations(self, duplicate_stats, deduplicated_data):
        """Create comprehensive visualizations"""
        print("\nCreating visualizations...")
        
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 1. Corrected System Distribution
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # True distribution pie chart
        if not deduplicated_data.empty:
            category_counts = deduplicated_data['category'].value_counts()
            labels = category_counts.index.tolist()
            sizes = category_counts.values.tolist()
            
            color_map = {
                'Highly Vulnerable': '#ff4444',
                'Healthy': '#44ff44', 
                'Vulnerable': '#ffaa44'
            }
            colors = [color_map.get(label, '#cccccc') for label in labels]
            
            ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax1.set_title('CORRECTED: System Vulnerability Distribution\n(Unique Systems Only)', fontweight='bold')
        
        # Original vs Corrected comparison
        categories = ['Highly Vulnerable', 'Vulnerable', 'Healthy']
        original_counts = [duplicate_stats['original_counts'].get(cat, 0) for cat in categories]
        corrected_counts = [duplicate_stats['corrected_counts'].get(cat, 0) for cat in categories]
        
        x = range(len(categories))
        width = 0.35
        
        ax2.bar([i - width/2 for i in x], original_counts, width, label='Original (with duplicates)', color='lightcoral', alpha=0.7)
        ax2.bar([i + width/2 for i in x], corrected_counts, width, label='Corrected (unique)', color='darkred')
        
        ax2.set_title('Original vs Corrected System Counts', fontweight='bold')
        ax2.set_ylabel('Number of Systems')
        ax2.set_xticks(x)
        ax2.set_xticklabels(categories, rotation=45)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.visualizations_dir, 'corrected_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        print("Visualizations created successfully!")
    
    def generate_executive_summary(self, duplicate_stats, deduplicated_data):
        """Generate comprehensive executive summary"""
        print("\nGenerating executive summary...")
        
        # Calculate key metrics
        total_systems = len(deduplicated_data)
        hv_count = duplicate_stats['corrected_counts'].get('Highly Vulnerable', 0)
        v_count = duplicate_stats['corrected_counts'].get('Vulnerable', 0)
        h_count = duplicate_stats['corrected_counts'].get('Healthy', 0)
        
        hv_percentage = (hv_count / total_systems * 100) if total_systems > 0 else 0
        
        # Determine risk level
        if hv_percentage > 30:
            risk_level = "CRITICAL"
        elif hv_percentage > 15:
            risk_level = "HIGH"
        elif hv_percentage > 5:
            risk_level = "MODERATE"
        else:
            risk_level = "LOW"
        
        # Generate report
        report_path = os.path.join(self.reports_dir, "FINAL_Executive_Summary.md")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# Vulnerability Analysis - FINAL Executive Summary\n\n")
            f.write(f"**Date:** {datetime.now().strftime('%B %d, %Y')}\n")
            f.write("**Prepared for:** Executive Leadership Team\n\n")
            
            f.write("## 🔍 Data Quality Correction\n\n")
            f.write("**IMPORTANT:** Initial analysis included duplicate records. This report shows corrected numbers.\n\n")
            
            f.write("## Critical Findings (CORRECTED)\n\n")
            f.write(f"- **Total Unique Systems:** {total_systems:,}\n")
            f.write(f"- **Highly Vulnerable:** {hv_count:,} ({hv_percentage:.1f}%)\n")
            f.write(f"- **Vulnerable:** {v_count:,}\n")
            f.write(f"- **Healthy:** {h_count:,}\n")
            f.write(f"- **Risk Level:** {risk_level}\n\n")
            
            f.write("## Data Quality Impact\n\n")
            f.write(f"- **Original Records:** {duplicate_stats['original_total']:,}\n")
            f.write(f"- **Duplicate Records Removed:** {duplicate_stats['duplicate_records']:,}\n")
            f.write(f"- **Duplicate Rate:** {(duplicate_stats['duplicate_records']/duplicate_stats['original_total']*100):.1f}%\n\n")
            
            f.write("## Immediate Actions Required\n\n")
            f.write(f"1. **PRIORITY 1:** Address {hv_count} highly vulnerable systems\n")
            f.write("2. **PRIORITY 2:** Investigate why systems remain vulnerable across multiple scans\n")
            f.write("3. **PRIORITY 3:** Implement data deduplication for future reporting\n")
            f.write("4. **PRIORITY 4:** Establish automated remediation processes\n\n")
            
            f.write("## Key Performance Indicators\n\n")
            f.write("| Metric | Current | Target (30 days) | Target (90 days) |\n")
            f.write("|--------|---------|------------------|------------------|\n")
            f.write(f"| Highly Vulnerable | {hv_count} ({hv_percentage:.1f}%) | <{int(total_systems*0.1)} (<10%) | <{int(total_systems*0.05)} (<5%) |\n")
            f.write(f"| Vulnerable | {v_count} | <{int(total_systems*0.1)} | <{int(total_systems*0.05)} |\n")
            f.write(f"| Healthy | {h_count} | >{int(total_systems*0.8)} | >{int(total_systems*0.9)} |\n\n")
            
            f.write("## Supporting Files\n\n")
            f.write("- **Deduplicated Dataset:** `reports/deduplicated_systems.csv`\n")
            f.write("- **Visualizations:** `visualizations/corrected_analysis.png`\n")
            f.write("- **Organized Data:** `organized_data/` folders\n\n")
            
            f.write("---\n")
            f.write("*This analysis provides accurate system counts for decision-making.*\n")
        
        print(f"Executive summary saved to: {report_path}")
        return report_path
    
    def run_complete_analysis(self):
        """Run the complete vulnerability analysis"""
        print("Starting Complete Vulnerability Analysis")
        print("=" * 60)
        
        # Step 1: Organize files
        print("\nStep 1: Organizing files...")
        organized_files = self.organize_files()
        
        # Step 2: Analyze duplicates and create corrected dataset
        duplicate_stats, deduplicated_data = self.analyze_duplicates_and_correct()
        
        # Step 3: Create visualizations
        self.create_visualizations(duplicate_stats, deduplicated_data)
        
        # Step 4: Generate executive summary
        report_path = self.generate_executive_summary(duplicate_stats, deduplicated_data)
        
        # Final summary
        print("\n" + "=" * 60)
        print("COMPLETE VULNERABILITY ANALYSIS FINISHED!")
        print("=" * 60)
        
        if deduplicated_data is not None and not deduplicated_data.empty:
            total_systems = len(deduplicated_data)
            hv_count = duplicate_stats['corrected_counts'].get('Highly Vulnerable', 0)
            hv_percentage = (hv_count / total_systems * 100) if total_systems > 0 else 0
            
            print(f"\nFINAL RESULTS:")
            print(f"  - Total Unique Systems: {total_systems:,}")
            print(f"  - Highly Vulnerable: {hv_count:,} ({hv_percentage:.1f}%)")
            print(f"  - Duplicates Removed: {duplicate_stats['duplicate_records']:,}")
            print(f"  - Executive Summary: {report_path}")
            
            if hv_percentage > 30:
                print(f"  - ⚠️  CRITICAL RISK LEVEL - Immediate action required!")
        
        return report_path, duplicate_stats, deduplicated_data

if __name__ == "__main__":
    # Run complete analysis
    analyzer = ComprehensiveVulnerabilityAnalyzer()
    analyzer.run_complete_analysis()
